# 知识库管理系统 - 第一期技术方案

## 📋 项目概述

构建一个基于大画布的知识库管理系统，支持从一个想法开始，通过AI辅助扩展和管理知识网络。第一期专注于核心的知识库管理功能。

## 🎯 第一期功能范围

### 核心功能
- ✅ 可视化知识图谱展示（大画布）
- ✅ 知识节点的创建、编辑、删除
- ✅ 节点间关系连接和管理
- ✅ 全文搜索和语义搜索
- ✅ 知识库数据持久化存储
- ✅ 基础的AI集成（想法扩展）

### 暂不实现
- ❌ 复杂的多模态支持
- ❌ 实时协作功能
- ❌ 高级AI推理
- ❌ 移动端适配

## 🏗️ 技术架构选择

### 整体架构：Tauri + React + Rust 后端

**选择理由：**
- 降低开发难度：前端使用熟悉的React生态
- 性能优秀：Rust后端处理数据密集型操作
- 跨平台：一套代码支持Windows/macOS/Linux
- 打包简单：单一可执行文件分发

## 📚 核心技术栈

### 前端技术栈
```json
{
  "框架": "React 18 + TypeScript",
  "画布库": "Konva.js + react-konva",
  "UI组件": "Ant Design",
  "状态管理": "Zustand",
  "图可视化": "Cytoscape.js",
  "构建工具": "Vite"
}
```

### 后端技术栈 (Rust)
```toml
[dependencies]
# 桌面应用框架
tauri = { version = "1.0", features = ["api-all"] }

# 数据库
surrealdb = "1.0"  # 图数据库 + 文档数据库
sled = "0.34"      # 嵌入式KV存储（缓存）

# 向量数据库
qdrant-client = "1.0"  # 向量搜索

# AI集成
async-openai = "0.14"  # OpenAI API客户端
reqwest = { version = "0.11", features = ["json"] }

# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 文本处理
tantivy = "0.19"  # 全文搜索引擎

# 工具库
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
```

## 🗄️ 数据存储方案

### 主数据库：SurrealDB
```rust
// 知识节点结构
#[derive(Serialize, Deserialize)]
struct KnowledgeNode {
    id: String,
    title: String,
    content: String,
    node_type: NodeType,
    position: Position,
    metadata: NodeMetadata,
    created_at: DateTime<Utc>,
    updated_at: DateTime<Utc>,
}

// 节点关系
#[derive(Serialize, Deserialize)]
struct NodeRelation {
    id: String,
    from_node: String,
    to_node: String,
    relation_type: RelationType,
    weight: f32,
    metadata: RelationMetadata,
}
```

### 向量存储：Qdrant
```rust
// 向量化的知识节点
struct VectorizedNode {
    node_id: String,
    embedding: Vec<f32>,
    metadata: HashMap<String, String>,
}
```

### 全文搜索：Tantivy
```rust
// 搜索索引结构
struct SearchIndex {
    title: String,
    content: String,
    node_type: String,
    tags: Vec<String>,
}
```

## 🎨 前端架构设计

### 组件结构
```
src/
├── components/
│   ├── Canvas/
│   │   ├── KnowledgeCanvas.tsx      # 主画布组件
│   │   ├── NodeRenderer.tsx         # 节点渲染器
│   │   ├── EdgeRenderer.tsx         # 连线渲染器
│   │   └── CanvasControls.tsx       # 画布控制器
│   ├── Sidebar/
│   │   ├── NodeEditor.tsx           # 节点编辑器
│   │   ├── SearchPanel.tsx          # 搜索面板
│   │   └── AIAssistant.tsx          # AI助手面板
│   └── Common/
│       ├── NodeCard.tsx             # 节点卡片
│       └── RelationLine.tsx         # 关系连线
├── stores/
│   ├── canvasStore.ts               # 画布状态
│   ├── knowledgeStore.ts            # 知识库状态
│   └── aiStore.ts                   # AI交互状态
├── services/
│   ├── tauriApi.ts                  # Tauri API调用
│   ├── knowledgeService.ts          # 知识库服务
│   └── aiService.ts                 # AI服务
└── types/
    ├── knowledge.ts                 # 知识库类型定义
    └── canvas.ts                    # 画布类型定义
```

### 核心组件实现

#### 主画布组件
```typescript
// KnowledgeCanvas.tsx
import { Stage, Layer } from 'react-konva';
import { useKnowledgeStore } from '../stores/knowledgeStore';

export const KnowledgeCanvas: React.FC = () => {
  const { nodes, edges, selectedNode } = useKnowledgeStore();
  const [stageScale, setStageScale] = useState(1);
  const [stagePos, setStagePos] = useState({ x: 0, y: 0 });

  const handleWheel = (e: any) => {
    e.evt.preventDefault();
    const scaleBy = 1.1;
    const stage = e.target.getStage();
    const oldScale = stage.scaleX();
    const newScale = e.evt.deltaY > 0 ? oldScale * scaleBy : oldScale / scaleBy;
    
    setStageScale(newScale);
  };

  return (
    <Stage
      width={window.innerWidth}
      height={window.innerHeight}
      onWheel={handleWheel}
      scaleX={stageScale}
      scaleY={stageScale}
      x={stagePos.x}
      y={stagePos.y}
      draggable
    >
      <Layer>
        {/* 渲染连线 */}
        {edges.map(edge => (
          <EdgeRenderer key={edge.id} edge={edge} />
        ))}
        
        {/* 渲染节点 */}
        {nodes.map(node => (
          <NodeRenderer 
            key={node.id} 
            node={node}
            isSelected={selectedNode?.id === node.id}
          />
        ))}
      </Layer>
    </Stage>
  );
};
```

## 🔧 后端核心服务

### Tauri 命令定义
```rust
// src-tauri/src/main.rs
use tauri::command;

#[command]
async fn create_knowledge_node(
    node: KnowledgeNodeInput,
    state: tauri::State<'_, AppState>
) -> Result<KnowledgeNode, String> {
    let knowledge_service = &state.knowledge_service;
    knowledge_service.create_node(node).await
        .map_err(|e| e.to_string())
}

#[command]
async fn search_knowledge(
    query: String,
    search_type: SearchType,
    state: tauri::State<'_, AppState>
) -> Result<Vec<SearchResult>, String> {
    let search_service = &state.search_service;
    match search_type {
        SearchType::FullText => search_service.full_text_search(&query).await,
        SearchType::Semantic => search_service.semantic_search(&query).await,
    }.map_err(|e| e.to_string())
}

#[command]
async fn ai_expand_idea(
    idea: String,
    context: Option<String>,
    state: tauri::State<'_, AppState>
) -> Result<Vec<KnowledgeNode>, String> {
    let ai_service = &state.ai_service;
    ai_service.expand_idea(&idea, context.as_deref()).await
        .map_err(|e| e.to_string())
}

fn main() {
    tauri::Builder::default()
        .manage(AppState::new())
        .invoke_handler(tauri::generate_handler![
            create_knowledge_node,
            search_knowledge,
            ai_expand_idea,
            // ... 其他命令
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

### 知识库服务
```rust
// src-tauri/src/services/knowledge_service.rs
use surrealdb::{Surreal, engine::local::Db};

pub struct KnowledgeService {
    db: Surreal<Db>,
    vector_client: qdrant_client::QdrantClient,
    search_index: tantivy::Index,
}

impl KnowledgeService {
    pub async fn create_node(&self, input: KnowledgeNodeInput) -> Result<KnowledgeNode> {
        // 1. 创建节点
        let node = KnowledgeNode {
            id: Uuid::new_v4().to_string(),
            title: input.title,
            content: input.content,
            node_type: input.node_type,
            position: input.position,
            metadata: NodeMetadata::default(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        // 2. 保存到图数据库
        let _: Option<KnowledgeNode> = self.db
            .create("knowledge_nodes")
            .content(&node)
            .await?;

        // 3. 向量化并存储
        if !node.content.is_empty() {
            let embedding = self.vectorize_text(&node.content).await?;
            self.store_vector(&node.id, embedding).await?;
        }

        // 4. 更新全文搜索索引
        self.update_search_index(&node).await?;

        Ok(node)
    }

    pub async fn semantic_search(&self, query: &str) -> Result<Vec<SearchResult>> {
        // 1. 向量化查询
        let query_vector = self.vectorize_text(query).await?;
        
        // 2. 向量相似性搜索
        let search_result = self.vector_client
            .search_points(&SearchPoints {
                collection_name: "knowledge_nodes".to_string(),
                vector: query_vector,
                limit: 20,
                with_payload: Some(true.into()),
                ..Default::default()
            })
            .await?;

        // 3. 获取详细节点信息
        let mut results = Vec::new();
        for point in search_result.result {
            if let Some(node_id) = point.payload.get("node_id") {
                if let Some(node) = self.get_node_by_id(&node_id.to_string()).await? {
                    results.push(SearchResult {
                        node,
                        score: point.score,
                        search_type: SearchType::Semantic,
                    });
                }
            }
        }

        Ok(results)
    }
}
```

## 🤖 AI 集成方案

### AI 服务实现
```rust
// src-tauri/src/services/ai_service.rs
use async_openai::{Client, types::*};

pub struct AIService {
    client: Client,
    knowledge_service: Arc<KnowledgeService>,
}

impl AIService {
    pub async fn expand_idea(&self, idea: &str, context: Option<&str>) -> Result<Vec<KnowledgeNode>> {
        let prompt = self.build_expansion_prompt(idea, context);
        
        let request = CreateChatCompletionRequestArgs::default()
            .model("gpt-3.5-turbo")
            .messages([
                ChatCompletionRequestSystemMessageArgs::default()
                    .content("你是一个知识管理助手，帮助用户扩展和组织想法。")
                    .build()?.into(),
                ChatCompletionRequestUserMessageArgs::default()
                    .content(prompt)
                    .build()?.into(),
            ])
            .build()?;

        let response = self.client.chat().completions().create(request).await?;
        
        if let Some(choice) = response.choices.first() {
            if let Some(content) = &choice.message.content {
                return self.parse_ai_response(content).await;
            }
        }

        Ok(vec![])
    }

    fn build_expansion_prompt(&self, idea: &str, context: Option<&str>) -> String {
        let context_part = context
            .map(|c| format!("现有上下文：{}\n\n", c))
            .unwrap_or_default();

        format!(
            "{}基于这个想法：'{}'\n\n请生成3-5个相关的知识点，每个知识点包含：\n\
            1. 标题（简洁明了）\n\
            2. 详细内容（100-200字）\n\
            3. 与原想法的关系类型\n\n\
            请以JSON格式返回，格式如下：\n\
            {{\n\
              \"nodes\": [\n\
                {{\n\
                  \"title\": \"知识点标题\",\n\
                  \"content\": \"详细内容\",\n\
                  \"relation_type\": \"extends|supports|contradicts|example\"\n\
                }}\n\
              ]\n\
            }}",
            context_part, idea
        )
    }
}
```

## 📦 项目结构

```
knowledge-management-system/
├── src-tauri/                    # Rust 后端
│   ├── src/
│   │   ├── main.rs              # 主入口
│   │   ├── commands/            # Tauri 命令
│   │   ├── services/            # 业务服务
│   │   ├── models/              # 数据模型
│   │   └── utils/               # 工具函数
│   ├── Cargo.toml
│   └── tauri.conf.json
├── src/                         # React 前端
│   ├── components/              # 组件
│   ├── stores/                  # 状态管理
│   ├── services/                # 前端服务
│   ├── types/                   # 类型定义
│   ├── styles/                  # 样式文件
│   └── main.tsx                 # 前端入口
├── public/                      # 静态资源
├── package.json
├── vite.config.ts
└── README.md
```

## 🚀 开发计划

### 第一阶段（2-3周）：基础框架
- [ ] 搭建 Tauri + React 项目结构
- [ ] 集成 SurrealDB 数据库
- [ ] 实现基础的节点 CRUD 操作
- [ ] 搭建基础画布组件

### 第二阶段（2-3周）：核心功能
- [ ] 实现可视化知识图谱
- [ ] 添加节点关系管理
- [ ] 集成全文搜索功能
- [ ] 实现基础 AI 扩展功能

### 第三阶段（1-2周）：优化完善
- [ ] 性能优化和用户体验改进
- [ ] 添加导入导出功能
- [ ] 完善错误处理和日志
- [ ] 编写文档和测试

## 💡 技术亮点

1. **低开发难度**：使用成熟的开源库，减少重复造轮子
2. **高性能**：Rust 后端 + Canvas 渲染，支持大规模知识图谱
3. **易扩展**：模块化设计，便于后续功能扩展
4. **跨平台**：一套代码支持多个操作系统
5. **离线优先**：本地数据库，无需网络连接即可使用

## 🔍 风险评估

### 技术风险
- **中等**：Tauri 生态相对较新，可能遇到兼容性问题
- **低**：选用的库都比较成熟稳定

### 开发风险
- **低**：技术栈相对简单，学习成本不高
- **中等**：AI 集成可能需要调试和优化

### 解决方案
- 提前进行技术验证
- 准备备选方案（如 Electron 替代 Tauri）
- 分阶段开发，及时调整方案
