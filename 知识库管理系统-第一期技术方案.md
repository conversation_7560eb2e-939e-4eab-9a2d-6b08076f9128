# 知识库管理系统 - 第一期技术方案

## 📋 项目概述

构建一个基于大画布的知识库管理系统，支持从一个想法开始，通过AI辅助扩展和管理知识网络。第一期专注于核心的知识库管理功能。

## 🎯 第一期功能范围

### 核心功能
- ✅ 可视化知识图谱展示（大画布）
- ✅ 知识节点的创建、编辑、删除
- ✅ 节点间关系连接和管理
- ✅ 全文搜索和语义搜索
- ✅ 知识库数据持久化存储
- ✅ 基础的AI集成（想法扩展）

### 暂不实现
- ❌ 复杂的多模态支持
- ❌ 实时协作功能
- ❌ 高级AI推理
- ❌ 移动端适配

## 🏗️ 技术架构选择

### 整体架构：Tauri + React + Rust 后端

**选择理由：**
- 降低开发难度：前端使用熟悉的React生态
- 性能优秀：Rust后端处理数据密集型操作
- 跨平台：一套代码支持Windows/macOS/Linux
- 打包简单：单一可执行文件分发

## 📚 核心技术栈

### 前端技术栈
```json
{
  "框架": "React 18 + TypeScript",
  "样式": "TailwindCSS",
  "画布库": "Konva.js + react-konva",
  "图可视化": "Cytoscape.js",
  "状态管理": "Zustand",
  "构建工具": "Vite",
  "UI组件": "Headless UI + 自定义组件"
}
```

### 后端技术栈 (Rust)
```toml
[dependencies]
# 桌面应用框架
tauri = { version = "1.0", features = ["api-all"] }

# 数据库
surrealdb = "1.0"  # 图数据库 + 文档数据库
sled = "0.34"      # 嵌入式KV存储（缓存）

# 向量数据库
qdrant-client = "1.0"  # 向量搜索

# AI集成 - 统一多模型支持
async-openai = "0.14"  # OpenAI API客户端
reqwest = { version = "0.11", features = ["json"] }
async-trait = "0.1"    # 异步trait支持

# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 文本处理
tantivy = "0.19"  # 全文搜索引擎

# 工具库
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
```

## 🗄️ 数据存储方案

### 主数据库：SurrealDB
```rust
// 知识节点结构
#[derive(Serialize, Deserialize)]
struct KnowledgeNode {
    id: String,
    title: String,
    content: String,
    node_type: NodeType,
    position: Position,
    metadata: NodeMetadata,
    created_at: DateTime<Utc>,
    updated_at: DateTime<Utc>,
}

// 节点关系
#[derive(Serialize, Deserialize)]
struct NodeRelation {
    id: String,
    from_node: String,
    to_node: String,
    relation_type: RelationType,
    weight: f32,
    metadata: RelationMetadata,
}
```

### 向量存储：Qdrant
```rust
// 向量化的知识节点
struct VectorizedNode {
    node_id: String,
    embedding: Vec<f32>,
    metadata: HashMap<String, String>,
}
```

### 全文搜索：Tantivy
```rust
// 搜索索引结构
struct SearchIndex {
    title: String,
    content: String,
    node_type: String,
    tags: Vec<String>,
}
```

## 🎨 前端架构设计

### 组件结构
```
src/
├── components/
│   ├── ui/                          # 基础UI组件(TailwindCSS)
│   │   ├── Button.tsx               # 按钮组件
│   │   ├── Input.tsx                # 输入框组件
│   │   ├── Modal.tsx                # 模态框组件
│   │   ├── Select.tsx               # 选择器组件
│   │   └── Tooltip.tsx              # 提示组件
│   ├── Canvas/
│   │   ├── KnowledgeCanvas.tsx      # 主画布组件
│   │   ├── NodeRenderer.tsx         # 节点渲染器
│   │   ├── EdgeRenderer.tsx         # 连线渲染器
│   │   └── CanvasControls.tsx       # 画布控制器
│   ├── Sidebar/
│   │   ├── NodeEditor.tsx           # 节点编辑器
│   │   ├── SearchPanel.tsx          # 搜索面板
│   │   ├── AIAssistant.tsx          # AI助手面板
│   │   └── ModelSelector.tsx        # AI模型选择器
│   └── Common/
│       ├── NodeCard.tsx             # 节点卡片
│       └── RelationLine.tsx         # 关系连线
├── stores/
│   ├── canvasStore.ts               # 画布状态
│   ├── knowledgeStore.ts            # 知识库状态
│   ├── aiStore.ts                   # AI交互状态
│   └── settingsStore.ts             # 设置状态(模型配置等)
├── services/
│   ├── tauriApi.ts                  # Tauri API调用
│   ├── knowledgeService.ts          # 知识库服务
│   └── aiService.ts                 # AI服务(统一接口)
├── types/
│   ├── knowledge.ts                 # 知识库类型定义
│   ├── canvas.ts                    # 画布类型定义
│   └── ai.ts                        # AI模型类型定义
└── styles/
    └── globals.css                  # TailwindCSS全局样式
```

### 核心组件实现

#### 主画布组件
```typescript
// KnowledgeCanvas.tsx
import React, { useState } from 'react';
import { Stage, Layer } from 'react-konva';
import { useKnowledgeStore } from '../stores/knowledgeStore';
import { NodeRenderer } from './NodeRenderer';
import { EdgeRenderer } from './EdgeRenderer';

export const KnowledgeCanvas: React.FC = () => {
  const { nodes, edges, selectedNode } = useKnowledgeStore();
  const [stageScale, setStageScale] = useState(1);
  const [stagePos, setStagePos] = useState({ x: 0, y: 0 });

  const handleWheel = (e: any) => {
    e.evt.preventDefault();
    const scaleBy = 1.1;
    const stage = e.target.getStage();
    const oldScale = stage.scaleX();
    const newScale = e.evt.deltaY > 0 ? oldScale * scaleBy : oldScale / scaleBy;

    setStageScale(Math.max(0.1, Math.min(3, newScale))); // 限制缩放范围
  };

  return (
    <div className="w-full h-full bg-gray-50 relative">
      <Stage
        width={window.innerWidth}
        height={window.innerHeight}
        onWheel={handleWheel}
        scaleX={stageScale}
        scaleY={stageScale}
        x={stagePos.x}
        y={stagePos.y}
        draggable
        className="cursor-grab active:cursor-grabbing"
      >
        <Layer>
          {/* 渲染连线 */}
          {edges.map(edge => (
            <EdgeRenderer key={edge.id} edge={edge} />
          ))}

          {/* 渲染节点 */}
          {nodes.map(node => (
            <NodeRenderer
              key={node.id}
              node={node}
              isSelected={selectedNode?.id === node.id}
            />
          ))}
        </Layer>
      </Stage>

      {/* 画布控制器 */}
      <div className="absolute bottom-4 right-4 flex flex-col gap-2">
        <button
          onClick={() => setStageScale(s => Math.min(3, s * 1.2))}
          className="bg-white shadow-lg rounded-lg p-2 hover:bg-gray-50 transition-colors"
        >
          <span className="text-lg">+</span>
        </button>
        <button
          onClick={() => setStageScale(s => Math.max(0.1, s / 1.2))}
          className="bg-white shadow-lg rounded-lg p-2 hover:bg-gray-50 transition-colors"
        >
          <span className="text-lg">-</span>
        </button>
        <button
          onClick={() => { setStageScale(1); setStagePos({ x: 0, y: 0 }); }}
          className="bg-white shadow-lg rounded-lg p-2 hover:bg-gray-50 transition-colors text-xs"
        >
          重置
        </button>
      </div>
    </div>
  );
};
```

## 🔧 后端核心服务

### Tauri 命令定义
```rust
// src-tauri/src/main.rs
use tauri::command;

#[command]
async fn create_knowledge_node(
    node: KnowledgeNodeInput,
    state: tauri::State<'_, AppState>
) -> Result<KnowledgeNode, String> {
    let knowledge_service = &state.knowledge_service;
    knowledge_service.create_node(node).await
        .map_err(|e| e.to_string())
}

#[command]
async fn search_knowledge(
    query: String,
    search_type: SearchType,
    state: tauri::State<'_, AppState>
) -> Result<Vec<SearchResult>, String> {
    let search_service = &state.search_service;
    match search_type {
        SearchType::FullText => search_service.full_text_search(&query).await,
        SearchType::Semantic => search_service.semantic_search(&query).await,
    }.map_err(|e| e.to_string())
}

#[command]
async fn ai_expand_idea(
    idea: String,
    context: Option<String>,
    state: tauri::State<'_, AppState>
) -> Result<Vec<KnowledgeNode>, String> {
    let ai_service = &state.ai_service;
    ai_service.expand_idea(&idea, context.as_deref()).await
        .map_err(|e| e.to_string())
}

fn main() {
    tauri::Builder::default()
        .manage(AppState::new())
        .invoke_handler(tauri::generate_handler![
            create_knowledge_node,
            search_knowledge,
            ai_expand_idea,
            // ... 其他命令
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

### 知识库服务
```rust
// src-tauri/src/services/knowledge_service.rs
use surrealdb::{Surreal, engine::local::Db};

pub struct KnowledgeService {
    db: Surreal<Db>,
    vector_client: qdrant_client::QdrantClient,
    search_index: tantivy::Index,
}

impl KnowledgeService {
    pub async fn create_node(&self, input: KnowledgeNodeInput) -> Result<KnowledgeNode> {
        // 1. 创建节点
        let node = KnowledgeNode {
            id: Uuid::new_v4().to_string(),
            title: input.title,
            content: input.content,
            node_type: input.node_type,
            position: input.position,
            metadata: NodeMetadata::default(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        // 2. 保存到图数据库
        let _: Option<KnowledgeNode> = self.db
            .create("knowledge_nodes")
            .content(&node)
            .await?;

        // 3. 向量化并存储
        if !node.content.is_empty() {
            let embedding = self.vectorize_text(&node.content).await?;
            self.store_vector(&node.id, embedding).await?;
        }

        // 4. 更新全文搜索索引
        self.update_search_index(&node).await?;

        Ok(node)
    }

    pub async fn semantic_search(&self, query: &str) -> Result<Vec<SearchResult>> {
        // 1. 向量化查询
        let query_vector = self.vectorize_text(query).await?;
        
        // 2. 向量相似性搜索
        let search_result = self.vector_client
            .search_points(&SearchPoints {
                collection_name: "knowledge_nodes".to_string(),
                vector: query_vector,
                limit: 20,
                with_payload: Some(true.into()),
                ..Default::default()
            })
            .await?;

        // 3. 获取详细节点信息
        let mut results = Vec::new();
        for point in search_result.result {
            if let Some(node_id) = point.payload.get("node_id") {
                if let Some(node) = self.get_node_by_id(&node_id.to_string()).await? {
                    results.push(SearchResult {
                        node,
                        score: point.score,
                        search_type: SearchType::Semantic,
                    });
                }
            }
        }

        Ok(results)
    }
}
```

## 🤖 AI 统一集成方案

### AI 模型抽象层设计
```rust
// src-tauri/src/services/ai/mod.rs
use async_trait::async_trait;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AIProvider {
    OpenAI,
    Claude,
    Gemini,
    QianWen,      // 阿里千问
    ChatGLM,      // 智谱清言
    Baidu,        // 百度文心一言
    Local,        // 本地模型
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIModelConfig {
    pub provider: AIProvider,
    pub model_name: String,
    pub api_key: String,
    pub base_url: Option<String>,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIMessage {
    pub role: String,
    pub content: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIResponse {
    pub content: String,
    pub usage: Option<AIUsage>,
    pub model: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIUsage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

// 统一的AI服务接口
#[async_trait]
pub trait AIService: Send + Sync {
    async fn chat_completion(
        &self,
        messages: Vec<AIMessage>,
        config: &AIModelConfig,
    ) -> Result<AIResponse, AIError>;

    async fn generate_embedding(
        &self,
        text: &str,
        config: &AIModelConfig,
    ) -> Result<Vec<f32>, AIError>;

    fn supports_streaming(&self) -> bool;

    async fn chat_completion_stream(
        &self,
        messages: Vec<AIMessage>,
        config: &AIModelConfig,
    ) -> Result<Box<dyn Stream<Item = Result<String, AIError>>>, AIError>;
}

#[derive(Debug, thiserror::Error)]
pub enum AIError {
    #[error("API调用失败: {0}")]
    ApiError(String),
    #[error("配置错误: {0}")]
    ConfigError(String),
    #[error("网络错误: {0}")]
    NetworkError(String),
    #[error("解析错误: {0}")]
    ParseError(String),
}
```

### 各大模型厂商实现
```rust
// src-tauri/src/services/ai/providers/openai.rs
use super::*;
use async_openai::{Client, types::*};

pub struct OpenAIService {
    client: Client,
}

#[async_trait]
impl AIService for OpenAIService {
    async fn chat_completion(
        &self,
        messages: Vec<AIMessage>,
        config: &AIModelConfig,
    ) -> Result<AIResponse, AIError> {
        let openai_messages: Vec<ChatCompletionRequestMessage> = messages
            .into_iter()
            .map(|msg| match msg.role.as_str() {
                "system" => ChatCompletionRequestSystemMessageArgs::default()
                    .content(msg.content)
                    .build()
                    .unwrap()
                    .into(),
                "user" => ChatCompletionRequestUserMessageArgs::default()
                    .content(msg.content)
                    .build()
                    .unwrap()
                    .into(),
                _ => ChatCompletionRequestAssistantMessageArgs::default()
                    .content(msg.content)
                    .build()
                    .unwrap()
                    .into(),
            })
            .collect();

        let request = CreateChatCompletionRequestArgs::default()
            .model(&config.model_name)
            .messages(openai_messages)
            .max_tokens(config.max_tokens.unwrap_or(1000))
            .temperature(config.temperature.unwrap_or(0.7))
            .build()
            .map_err(|e| AIError::ConfigError(e.to_string()))?;

        let response = self.client
            .chat()
            .completions()
            .create(request)
            .await
            .map_err(|e| AIError::ApiError(e.to_string()))?;

        if let Some(choice) = response.choices.first() {
            if let Some(content) = &choice.message.content {
                return Ok(AIResponse {
                    content: content.clone(),
                    usage: response.usage.map(|u| AIUsage {
                        prompt_tokens: u.prompt_tokens,
                        completion_tokens: u.completion_tokens,
                        total_tokens: u.total_tokens,
                    }),
                    model: response.model,
                });
            }
        }

        Err(AIError::ApiError("No response content".to_string()))
    }

    async fn generate_embedding(
        &self,
        text: &str,
        config: &AIModelConfig,
    ) -> Result<Vec<f32>, AIError> {
        let request = CreateEmbeddingRequestArgs::default()
            .model("text-embedding-ada-002")
            .input(text)
            .build()
            .map_err(|e| AIError::ConfigError(e.to_string()))?;

        let response = self.client
            .embeddings()
            .create(request)
            .await
            .map_err(|e| AIError::ApiError(e.to_string()))?;

        if let Some(embedding) = response.data.first() {
            Ok(embedding.embedding.clone())
        } else {
            Err(AIError::ApiError("No embedding data".to_string()))
        }
    }

    fn supports_streaming(&self) -> bool {
        true
    }

    async fn chat_completion_stream(
        &self,
        messages: Vec<AIMessage>,
        config: &AIModelConfig,
    ) -> Result<Box<dyn Stream<Item = Result<String, AIError>>>, AIError> {
        // 实现流式响应
        todo!("实现OpenAI流式响应")
    }
}
```

### 统一AI管理器
```rust
// src-tauri/src/services/ai/manager.rs
use std::collections::HashMap;
use std::sync::Arc;

pub struct AIManager {
    providers: HashMap<AIProvider, Arc<dyn AIService>>,
    default_config: AIModelConfig,
}

impl AIManager {
    pub fn new() -> Self {
        let mut providers: HashMap<AIProvider, Arc<dyn AIService>> = HashMap::new();

        // 注册各个提供商
        providers.insert(AIProvider::OpenAI, Arc::new(OpenAIService::new()));
        providers.insert(AIProvider::Claude, Arc::new(ClaudeService::new()));
        providers.insert(AIProvider::Gemini, Arc::new(GeminiService::new()));
        providers.insert(AIProvider::QianWen, Arc::new(QianWenService::new()));
        providers.insert(AIProvider::ChatGLM, Arc::new(ChatGLMService::new()));
        providers.insert(AIProvider::Baidu, Arc::new(BaiduService::new()));

        Self {
            providers,
            default_config: AIModelConfig {
                provider: AIProvider::OpenAI,
                model_name: "gpt-3.5-turbo".to_string(),
                api_key: String::new(),
                base_url: None,
                max_tokens: Some(1000),
                temperature: Some(0.7),
            },
        }
    }

    pub async fn expand_idea(
        &self,
        idea: &str,
        context: Option<&str>,
        config: Option<&AIModelConfig>,
    ) -> Result<Vec<KnowledgeNode>, AIError> {
        let config = config.unwrap_or(&self.default_config);
        let service = self.providers
            .get(&config.provider)
            .ok_or_else(|| AIError::ConfigError("Unsupported provider".to_string()))?;

        let prompt = self.build_expansion_prompt(idea, context);
        let messages = vec![
            AIMessage {
                role: "system".to_string(),
                content: "你是一个知识管理助手，帮助用户扩展和组织想法。".to_string(),
            },
            AIMessage {
                role: "user".to_string(),
                content: prompt,
            },
        ];

        let response = service.chat_completion(messages, config).await?;
        self.parse_ai_response(&response.content).await
    }

    pub async fn get_available_models(&self, provider: AIProvider) -> Vec<String> {
        match provider {
            AIProvider::OpenAI => vec![
                "gpt-4".to_string(),
                "gpt-4-turbo".to_string(),
                "gpt-3.5-turbo".to_string(),
            ],
            AIProvider::Claude => vec![
                "claude-3-opus".to_string(),
                "claude-3-sonnet".to_string(),
                "claude-3-haiku".to_string(),
            ],
            AIProvider::Gemini => vec![
                "gemini-pro".to_string(),
                "gemini-pro-vision".to_string(),
            ],
            AIProvider::QianWen => vec![
                "qwen-turbo".to_string(),
                "qwen-plus".to_string(),
                "qwen-max".to_string(),
            ],
            AIProvider::ChatGLM => vec![
                "glm-4".to_string(),
                "glm-3-turbo".to_string(),
            ],
            AIProvider::Baidu => vec![
                "ernie-bot".to_string(),
                "ernie-bot-turbo".to_string(),
            ],
            AIProvider::Local => vec![
                "llama2".to_string(),
                "mistral".to_string(),
            ],
        }
    }

    fn build_expansion_prompt(&self, idea: &str, context: Option<&str>) -> String {
        let context_part = context
            .map(|c| format!("现有上下文：{}\n\n", c))
            .unwrap_or_default();

        format!(
            "{}基于这个想法：'{}'\n\n请生成3-5个相关的知识点，每个知识点包含：\n\
            1. 标题（简洁明了）\n\
            2. 详细内容（100-200字）\n\
            3. 与原想法的关系类型\n\n\
            请以JSON格式返回，格式如下：\n\
            {{\n\
              \"nodes\": [\n\
                {{\n\
                  \"title\": \"知识点标题\",\n\
                  \"content\": \"详细内容\",\n\
                  \"relation_type\": \"extends|supports|contradicts|example\"\n\
                }}\n\
              ]\n\
            }}",
            context_part, idea
        )
    }

    async fn parse_ai_response(&self, content: &str) -> Result<Vec<KnowledgeNode>, AIError> {
        // 解析AI响应并转换为知识节点
        // 实现JSON解析逻辑
        todo!("实现AI响应解析")
    }
}
```

### 前端AI模型管理
```typescript
// src/types/ai.ts
export interface AIProvider {
  id: string;
  name: string;
  icon: string;
  models: AIModel[];
  requiresApiKey: boolean;
  baseUrl?: string;
}

export interface AIModel {
  id: string;
  name: string;
  description: string;
  maxTokens: number;
  supportsFunctions: boolean;
  supportsStreaming: boolean;
}

export interface AIConfig {
  provider: string;
  model: string;
  apiKey: string;
  baseUrl?: string;
  temperature: number;
  maxTokens: number;
}

// src/stores/settingsStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface SettingsState {
  aiConfigs: Record<string, AIConfig>;
  currentAIProvider: string;
  availableProviders: AIProvider[];

  // Actions
  setAIConfig: (providerId: string, config: AIConfig) => void;
  setCurrentProvider: (providerId: string) => void;
  loadAvailableProviders: () => Promise<void>;
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      aiConfigs: {},
      currentAIProvider: 'openai',
      availableProviders: [],

      setAIConfig: (providerId, config) => {
        set(state => ({
          aiConfigs: {
            ...state.aiConfigs,
            [providerId]: config
          }
        }));
      },

      setCurrentProvider: (providerId) => {
        set({ currentAIProvider: providerId });
      },

      loadAvailableProviders: async () => {
        try {
          const providers = await invoke('get_available_ai_providers');
          set({ availableProviders: providers });
        } catch (error) {
          console.error('Failed to load AI providers:', error);
        }
      },
    }),
    {
      name: 'settings-storage',
    }
  )
);

// src/components/Sidebar/ModelSelector.tsx
import React, { useState } from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { Button } from '../ui/Button';
import { Select } from '../ui/Select';
import { Input } from '../ui/Input';
import { Modal } from '../ui/Modal';

export const ModelSelector: React.FC = () => {
  const {
    availableProviders,
    currentAIProvider,
    aiConfigs,
    setCurrentProvider,
    setAIConfig,
  } = useSettingsStore();

  const [showConfig, setShowConfig] = useState(false);
  const [tempConfig, setTempConfig] = useState<AIConfig | null>(null);

  const currentProvider = availableProviders.find(p => p.id === currentAIProvider);
  const currentConfig = aiConfigs[currentAIProvider];

  const handleConfigSave = () => {
    if (tempConfig) {
      setAIConfig(currentAIProvider, tempConfig);
      setShowConfig(false);
      setTempConfig(null);
    }
  };

  return (
    <div className="p-4 border-b border-gray-200">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-700">AI 模型</h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            setTempConfig(currentConfig || {
              provider: currentAIProvider,
              model: currentProvider?.models[0]?.id || '',
              apiKey: '',
              temperature: 0.7,
              maxTokens: 1000,
            });
            setShowConfig(true);
          }}
          className="text-xs"
        >
          配置
        </Button>
      </div>

      <Select
        value={currentAIProvider}
        onValueChange={setCurrentProvider}
        className="w-full mb-2"
      >
        {availableProviders.map(provider => (
          <option key={provider.id} value={provider.id}>
            {provider.name}
          </option>
        ))}
      </Select>

      {currentProvider && currentConfig && (
        <div className="text-xs text-gray-500">
          模型: {currentConfig.model}
          {currentConfig.apiKey && (
            <span className="ml-2 text-green-600">✓ 已配置</span>
          )}
        </div>
      )}

      {/* 配置模态框 */}
      <Modal
        isOpen={showConfig}
        onClose={() => setShowConfig(false)}
        title={`配置 ${currentProvider?.name}`}
      >
        {tempConfig && currentProvider && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                模型
              </label>
              <Select
                value={tempConfig.model}
                onValueChange={(value) =>
                  setTempConfig({ ...tempConfig, model: value })
                }
              >
                {currentProvider.models.map(model => (
                  <option key={model.id} value={model.id}>
                    {model.name}
                  </option>
                ))}
              </Select>
            </div>

            {currentProvider.requiresApiKey && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  API Key
                </label>
                <Input
                  type="password"
                  value={tempConfig.apiKey}
                  onChange={(e) =>
                    setTempConfig({ ...tempConfig, apiKey: e.target.value })
                  }
                  placeholder="输入 API Key"
                />
              </div>
            )}

            {currentProvider.baseUrl && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Base URL
                </label>
                <Input
                  value={tempConfig.baseUrl || currentProvider.baseUrl}
                  onChange={(e) =>
                    setTempConfig({ ...tempConfig, baseUrl: e.target.value })
                  }
                  placeholder="API 基础地址"
                />
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Temperature
                </label>
                <Input
                  type="number"
                  min="0"
                  max="2"
                  step="0.1"
                  value={tempConfig.temperature}
                  onChange={(e) =>
                    setTempConfig({
                      ...tempConfig,
                      temperature: parseFloat(e.target.value)
                    })
                  }
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Tokens
                </label>
                <Input
                  type="number"
                  min="1"
                  max="4000"
                  value={tempConfig.maxTokens}
                  onChange={(e) =>
                    setTempConfig({
                      ...tempConfig,
                      maxTokens: parseInt(e.target.value)
                    })
                  }
                />
              </div>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowConfig(false)}
              >
                取消
              </Button>
              <Button onClick={handleConfigSave}>
                保存
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};
```

### TailwindCSS 配置
```javascript
// tailwind.config.js
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.2s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}

// src/styles/globals.css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: Inter, system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
}
```

## 📦 项目结构

```
knowledge-management-system/
├── src-tauri/                    # Rust 后端
│   ├── src/
│   │   ├── main.rs              # 主入口
│   │   ├── commands/            # Tauri 命令
│   │   ├── services/            # 业务服务
│   │   ├── models/              # 数据模型
│   │   └── utils/               # 工具函数
│   ├── Cargo.toml
│   └── tauri.conf.json
├── src/                         # React 前端
│   ├── components/              # 组件
│   ├── stores/                  # 状态管理
│   ├── services/                # 前端服务
│   ├── types/                   # 类型定义
│   ├── styles/                  # 样式文件
│   └── main.tsx                 # 前端入口
├── public/                      # 静态资源
├── package.json
├── vite.config.ts
├── tailwind.config.js
├── postcss.config.js
└── README.md

## 📋 前端依赖配置

### package.json
```json
{
  "name": "knowledge-management-system",
  "private": true,
  "version": "0.1.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "tauri": "tauri"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-konva": "^18.2.10",
    "konva": "^9.2.0",
    "cytoscape": "^3.26.0",
    "cytoscape-react": "^2.0.0",
    "zustand": "^4.4.1",
    "@tauri-apps/api": "^1.5.0",
    "@headlessui/react": "^1.7.17",
    "@heroicons/react": "^2.0.18",
    "clsx": "^2.0.0",
    "framer-motion": "^10.16.4"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@vitejs/plugin-react": "^4.0.3",
    "typescript": "^5.0.2",
    "vite": "^4.4.5",
    "tailwindcss": "^3.3.3",
    "autoprefixer": "^10.4.15",
    "postcss": "^8.4.29",
    "@tailwindcss/forms": "^0.5.6",
    "@tailwindcss/typography": "^0.5.10",
    "@tauri-apps/cli": "^1.5.0"
  }
}
```

### vite.config.ts
```typescript
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

export default defineConfig(async () => ({
  plugins: [react()],
  clearScreen: false,
  server: {
    port: 1420,
    strictPort: true,
    watch: {
      ignored: ["**/src-tauri/**"],
    },
  },
  build: {
    target: process.env.TAURI_PLATFORM == "windows" ? "chrome105" : "safari13",
    minify: !process.env.TAURI_DEBUG ? "esbuild" : false,
    sourcemap: !!process.env.TAURI_DEBUG,
  },
}));
```
```

## 🚀 开发计划

### 第一阶段（2-3周）：基础框架搭建
- [ ] 搭建 Tauri + React + TypeScript + TailwindCSS 项目结构
- [ ] 配置开发环境和构建流程
- [ ] 集成 SurrealDB 数据库
- [ ] 实现基础的节点 CRUD 操作
- [ ] 搭建基础画布组件（Konva.js）
- [ ] 创建基础 UI 组件库（TailwindCSS）

### 第二阶段（3-4周）：核心功能实现
- [ ] 实现可视化知识图谱展示
- [ ] 添加节点关系管理和连线功能
- [ ] 集成 Qdrant 向量数据库
- [ ] 实现 Tantivy 全文搜索功能
- [ ] 构建 AI 模型统一抽象层
- [ ] 实现多个主流 AI 提供商支持（OpenAI、Claude、千问等）
- [ ] 开发 AI 模型配置和选择界面
- [ ] 实现基础 AI 想法扩展功能

### 第三阶段（2-3周）：AI 增强和优化
- [ ] 完善 AI 响应解析和知识节点生成
- [ ] 实现语义搜索功能
- [ ] 添加 AI 流式响应支持
- [ ] 优化画布性能（大规模节点渲染）
- [ ] 实现知识库导入导出功能
- [ ] 添加快捷键和用户体验优化

### 第四阶段（1-2周）：测试和发布准备
- [ ] 完善错误处理和日志系统
- [ ] 编写单元测试和集成测试
- [ ] 性能测试和优化
- [ ] 编写用户文档和开发文档
- [ ] 准备发布包和安装程序

## 💡 技术亮点

1. **现代化前端技术栈**：React + TypeScript + TailwindCSS，开发效率高
2. **统一AI模型接口**：支持多个主流AI提供商，易于扩展新模型
3. **高性能渲染**：Rust 后端 + Konva.js Canvas 渲染，支持大规模知识图谱
4. **模块化设计**：清晰的架构分层，便于维护和扩展
5. **跨平台支持**：一套代码支持 Windows/macOS/Linux
6. **离线优先**：本地数据库存储，支持离线使用
7. **响应式设计**：TailwindCSS 确保良好的用户体验

## 🎯 AI 模型支持策略

### 已规划支持的模型
- **OpenAI**: GPT-4, GPT-3.5-turbo
- **Anthropic**: Claude-3 系列
- **Google**: Gemini Pro 系列
- **阿里云**: 千问系列
- **智谱AI**: ChatGLM 系列
- **百度**: 文心一言系列
- **本地模型**: Llama2, Mistral 等

### 扩展新模型的步骤
1. 实现对应的 `AIService` trait
2. 在 `AIManager` 中注册新提供商
3. 更新前端模型列表配置
4. 添加相应的配置界面

## 🔍 风险评估与应对

### 技术风险
- **中等 - Tauri 生态**：相对较新，可能遇到兼容性问题
  - *应对*：提前技术验证，准备 Electron 备选方案
- **低 - 开源库选择**：选用成熟稳定的库
  - *应对*：持续关注库的更新和社区活跃度

### 开发风险
- **低 - 前端技术栈**：React + TypeScript 成熟稳定
- **中等 - AI 集成复杂度**：多模型适配需要大量测试
  - *应对*：分阶段实现，先支持主流模型
- **中等 - 性能优化**：大规模图谱渲染可能遇到性能瓶颈
  - *应对*：实现虚拟化渲染，按需加载节点

### 业务风险
- **低 - 用户接受度**：知识管理是刚需
- **中等 - AI API 成本**：频繁调用可能产生较高费用
  - *应对*：实现本地缓存，支持本地模型

### 解决方案
- **技术验证优先**：核心技术提前验证可行性
- **分阶段开发**：按功能模块逐步实现，降低风险
- **持续集成**：建立自动化测试和部署流程
- **用户反馈**：早期发布 MVP 版本收集用户反馈
